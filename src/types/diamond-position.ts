/**
 * 金刚位配置管理类型定义
 * 支持多种图标类型和响应式布局
 */

/**
 * 图标类型枚举
 */
export type IconType = 'url' | 'svg' | 'base64' | 'emoji'

/**
 * 平台类型枚举
 */
export type PlatformType = 'ALL' | 'IOS' | 'ANDROID' | 'H5'

/**
 * 设备类型枚举（用于响应式预览）
 */
export type DeviceType = 'mobile' | 'tablet' | 'desktop'

/**
 * 金刚位状态枚举
 */
export type DiamondPositionStatus = 0 | 1

/**
 * 金刚位数据接口
 */
export interface DiamondPosition {
  /** 主键ID */
  id?: number

  /** 金刚位名称 */
  name: string

  /** 图标内容（URL、SVG代码、Base64编码或表情符号） */
  icon: string

  /** 图标类型 */
  iconType: IconType

  /** 跳转链接 */
  url: string

  /** 排序序号 */
  sortOrder: number

  /** 状态：0-禁用，1-启用 */
  status: DiamondPositionStatus

  /** 描述信息 */
  description?: string

  /** 平台类型 */
  platform: PlatformType

  /** 分组ID */
  groupId?: number

  /** 是否热门 */
  isHot?: boolean

  /** 是否新品 */
  isNew?: boolean

  /** 创建时间 */
  createdTime?: string

  /** 更新时间 */
  updatedTime?: string

  /** 创建人 */
  createdBy?: string

  /** 更新人 */
  updatedBy?: string

  /** 租户ID */
  tenantId?: string

  /** 创建部门 */
  createDept?: number

  /** 删除标志 */
  delFlag?: string
}

/**
 * 金刚位查询参数
 */
export interface DiamondPositionQuery {
  /** 分页参数 */
  pageNum?: number
  pageSize?: number

  /** 名称关键词 */
  name?: string

  /** 图标类型筛选 */
  iconType?: IconType

  /** 平台筛选 */
  platform?: PlatformType

  /** 状态筛选 */
  status?: DiamondPositionStatus

  /** 分组ID筛选 */
  groupId?: number

  /** 是否热门筛选 */
  isHot?: boolean

  /** 是否新品筛选 */
  isNew?: boolean

  /** 排序字段 */
  orderBy?: string

  /** 排序方向 */
  orderDirection?: 'asc' | 'desc'

  /** 日期范围参数 */
  params?: Record<string, any>
}

/**
 * 金刚位创建请求
 */
export interface CreateDiamondPositionRequest {
  /** 金刚位名称 */
  name: string

  /** 图标内容 */
  icon: string

  /** 图标类型 */
  iconType: IconType

  /** 跳转链接 */
  url: string

  /** 排序序号 */
  sortOrder?: number

  /** 状态 */
  status?: DiamondPositionStatus

  /** 描述信息 */
  description?: string

  /** 平台类型 */
  platform?: PlatformType

  /** 分组ID */
  groupId?: number

  /** 是否热门 */
  isHot?: boolean

  /** 是否新品 */
  isNew?: boolean
}

/**
 * 金刚位更新请求
 */
export interface UpdateDiamondPositionRequest extends Partial<CreateDiamondPositionRequest> {
  /** 主键ID */
  id: number
}

/**
 * 金刚位排序更新请求
 */
export interface UpdateSortOrderRequest {
  /** 主键ID */
  id: number

  /** 新的排序序号 */
  sortOrder: number
}

/**
 * 批量排序请求
 */
export interface BatchSortRequest {
  /** 排序项目列表 */
  items: UpdateSortOrderRequest[]
}

/**
 * 分页响应数据
 */
export interface PaginatedResponse<T> {
  /** 数据列表 */
  data: T[]

  /** 总记录数 */
  total: number

  /** 当前页码 */
  page: number

  /** 每页大小 */
  pageSize: number

  /** 总页数 */
  totalPages: number
}

/**
 * API响应格式
 */
export interface ApiResponse<T = any> {
  /** 是否成功 */
  success: boolean

  /** 响应数据 */
  data: T

  /** 响应消息 */
  message: string

  /** 响应码 */
  code?: number

  /** 时间戳 */
  timestamp?: string
}

/**
 * 图标验证规则
 */
export interface IconValidationRule {
  /** 图标类型 */
  type: IconType

  /** 验证正则表达式 */
  pattern: RegExp

  /** 错误提示信息 */
  message: string

  /** 最大长度限制 */
  maxLength?: number
}

/**
 * 文件上传参数
 */
export interface FileUploadParams {
  /** 文件对象 */
  file: File

  /** 目标图标类型 */
  targetType: IconType

  /** 最大文件大小（字节） */
  maxSize?: number
}

/**
 * 图片上传响应
 */
export interface ImageUploadResponse {
  /** 上传后的URL地址 */
  url?: string

  /** Base64编码内容 */
  base64?: string

  /** 原始文件名 */
  originalName: string

  /** 文件大小 */
  size: number

  /** MIME类型 */
  mimeType: string
}

/**
 * SVG上传响应
 */
export interface SvgUploadResponse {
  /** SVG代码内容 */
  svgContent: string

  /** 原始文件名 */
  originalName: string

  /** 文件大小 */
  size: number
}

/**
 * 设备预览配置
 */
export interface DevicePreviewConfig {
  /** 设备类型 */
  type: DeviceType

  /** 设备名称 */
  name: string

  /** 屏幕宽度 */
  width: number

  /** 屏幕高度 */
  height: number

  /** 网格列数 */
  columns: number

  /** 网格间距 */
  gap: number

  /** 图标大小 */
  iconSize: number
}

/**
 * 响应式网格配置
 */
export interface ResponsiveGridConfig {
  /** 移动端配置 */
  mobile: DevicePreviewConfig

  /** 平板端配置 */
  tablet: DevicePreviewConfig

  /** 桌面端配置 */
  desktop: DevicePreviewConfig
}

/**
 * 拖拽排序事件数据
 */
export interface DragSortEvent {
  /** 拖拽的项目ID */
  draggedId: number

  /** 目标位置ID */
  targetId: number

  /** 新的排序列表 */
  newOrder: UpdateSortOrderRequest[]
}

/**
 * 表格列配置
 */
export interface TableColumn {
  /** 列键名 */
  key: string

  /** 列标题 */
  label: string

  /** 是否可见 */
  visible: boolean

  /** 列宽度 */
  width?: number

  /** 是否可排序 */
  sortable?: boolean

  /** 对齐方式 */
  align?: 'left' | 'center' | 'right'
}

/**
 * 表格数据信息
 */
export interface TableDataInfo<T> {
  /** 总记录数 */
  total: number

  /** 列表数据 */
  rows: T[]

  /** 消息状态码 */
  code: number

  /** 消息内容 */
  msg: string
}

/**
 * 导出参数
 */
export interface ExportParams extends DiamondPositionQuery {
  /** 导出格式 */
  format?: 'excel' | 'csv'

  /** 导出文件名 */
  filename?: string
}

/**
 * 图标类型选项
 */
export interface IconTypeOption {
  /** 选项值 */
  value: IconType

  /** 选项标签 */
  label: string

  /** 选项描述 */
  description: string

  /** 选项图标 */
  icon?: string

  /** 是否支持文件上传 */
  supportUpload?: boolean
}

/**
 * 平台选项
 */
export interface PlatformOption {
  /** 选项值 */
  value: PlatformType

  /** 选项标签 */
  label: string

  /** 选项颜色 */
  color?: string
}

/**
 * 常量定义
 */
export const ICON_TYPES: IconTypeOption[] = [
  {
    value: 'url',
    label: 'URL链接',
    description: '网络图片链接或本地路径',
    icon: 'Link',
    supportUpload: true
  },
  {
    value: 'emoji',
    label: '表情符号',
    description: 'Unicode表情符号',
    icon: 'Smile',
    supportUpload: false
  },
  {
    value: 'svg',
    label: 'SVG代码',
    description: 'SVG矢量图代码',
    icon: 'Code',
    supportUpload: true
  },
  {
    value: 'base64',
    label: 'Base64编码',
    description: 'Base64编码的图片',
    icon: 'Document',
    supportUpload: true
  }
]

export const PLATFORMS: PlatformOption[] = [
  { value: 'ALL', label: '全平台', color: 'primary' },
  { value: 'IOS', label: 'iOS', color: 'success' },
  { value: 'ANDROID', label: 'Android', color: 'warning' },
  { value: 'H5', label: 'H5', color: 'info' }
]

export const DEVICE_CONFIGS: ResponsiveGridConfig = {
  mobile: {
    type: 'mobile',
    name: '手机端',
    width: 375,
    height: 667,
    columns: 4,
    gap: 12,
    iconSize: 48
  },
  tablet: {
    type: 'tablet',
    name: '平板端',
    width: 768,
    height: 1024,
    columns: 6,
    gap: 16,
    iconSize: 56
  },
  desktop: {
    type: 'desktop',
    name: '桌面端',
    width: 1200,
    height: 800,
    columns: 8,
    gap: 20,
    iconSize: 64
  }
}

/**
 * 图标验证规则
 */
export const ICON_VALIDATION_RULES: Record<IconType, IconValidationRule> = {
  url: {
    type: 'url',
    pattern: /^(https?:\/\/|\/)/,
    message: 'URL类型图标必须以http://、https://或/开头',
    maxLength: 500
  },
  svg: {
    type: 'svg',
    pattern: /^<svg[\s\S]*<\/svg>$/,
    message: 'SVG类型图标必须以<svg开头并以</svg>结尾',
    maxLength: 10000
  },
  base64: {
    type: 'base64',
    pattern: /^data:image\//,
    message: 'Base64类型图标必须以data:image/开头',
    maxLength: 50000
  },
  emoji: {
    type: 'emoji',
    pattern: /^.{1,10}$/,
    message: '表情符号类型图标长度不能超过10个字符',
    maxLength: 10
  }
}

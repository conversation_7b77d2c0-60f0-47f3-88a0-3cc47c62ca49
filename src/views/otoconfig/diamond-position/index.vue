<template>
  <div class="diamond-position-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">金刚位配置管理</h1>
        <p class="page-description">管理应用首页的金刚位图标，支持多种图标类型和拖拽排序</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>
          新增金刚位
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <el-card shadow="never">
        <el-form :model="queryParams" inline>
          <el-form-item label="名称">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入金刚位名称"
              clearable
              style="width: 200px"
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          
          <el-form-item label="图标类型">
            <el-select
              v-model="queryParams.iconType"
              placeholder="请选择图标类型"
              clearable
              style="width: 150px"
            >
              <el-option
                v-for="type in iconTypeOptions"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="平台">
            <el-select
              v-model="queryParams.platform"
              placeholder="请选择平台"
              clearable
              style="width: 120px"
            >
              <el-option
                v-for="platform in platformOptions"
                :key="platform.value"
                :label="platform.label"
                :value="platform.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="状态">
            <el-select
              v-model="queryParams.status"
              placeholder="请选择状态"
              clearable
              style="width: 120px"
            >
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 视图切换和批量操作 -->
    <div class="toolbar-section">
      <div class="toolbar-left">
        <el-radio-group v-model="viewMode" @change="handleViewModeChange">
          <el-radio-button label="grid">
            <el-icon><Grid /></el-icon>
            网格视图
          </el-radio-button>
          <el-radio-button label="table">
            <el-icon><List /></el-icon>
            表格视图
          </el-radio-button>
        </el-radio-group>

        <el-button-group class="ml-4">
          <el-button
            :disabled="selectedItems.length === 0"
            @click="handleBatchEnable"
          >
            <el-icon><Check /></el-icon>
            批量启用
          </el-button>
          <el-button
            :disabled="selectedItems.length === 0"
            @click="handleBatchDisable"
          >
            <el-icon><Close /></el-icon>
            批量禁用
          </el-button>
          <el-button
            type="danger"
            :disabled="selectedItems.length === 0"
            @click="handleBatchDelete"
          >
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
        </el-button-group>
      </div>

      <div class="toolbar-right">
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        
        <el-dropdown @command="handleExport">
          <el-button>
            <el-icon><Download /></el-icon>
            导出
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="excel">导出Excel</el-dropdown-item>
              <el-dropdown-item command="csv">导出CSV</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-section">
      <!-- 网格视图 -->
      <DiamondPositionGrid
        v-if="viewMode === 'grid'"
        v-model:selected="selectedItems"
        :items="items"
        :loading="loading"
        @edit="handleEdit"
        @delete="handleDelete"
        @toggle-status="handleToggleStatus"
        @sort-change="handleSortChange"
      />

      <!-- 表格视图 -->
      <DiamondPositionTable
        v-else
        v-model:selected="selectedItems"
        :items="items"
        :loading="loading"
        :total="total"
        :query-params="queryParams"
        @edit="handleEdit"
        @delete="handleDelete"
        @toggle-status="handleToggleStatus"
        @page-change="handlePageChange"
        @size-change="handleSizeChange"
      />
    </div>

    <!-- 编辑对话框 -->
    <DiamondPositionDialog
      v-model:visible="dialogVisible"
      :form-data="currentItem"
      :mode="dialogMode"
      @confirm="handleDialogConfirm"
    />

    <!-- 多设备预览对话框 -->
    <DevicePreviewDialog
      v-model:visible="previewVisible"
      :items="enabledItems"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  Refresh,
  Grid,
  List,
  Check,
  Close,
  Delete,
  Download,
  ArrowDown
} from '@element-plus/icons-vue'

// 组件导入
import DiamondPositionGrid from './components/DiamondPositionGrid.vue'
import DiamondPositionTable from './components/DiamondPositionTable.vue'
import DiamondPositionDialog from './components/DiamondPositionDialog.vue'
import DevicePreviewDialog from './components/DevicePreviewDialog.vue'

// 组合式函数导入
import { useDiamondPosition } from '@/composables/useDiamondPosition'
import type { 
  DiamondPosition, 
  UpdateSortOrderRequest,
  IconType,
  PlatformType 
} from '@/types/diamond-position'
import { ICON_TYPES, PLATFORMS } from '@/types/diamond-position'

// 页面状态
const viewMode = ref<'grid' | 'table'>('grid')
const selectedItems = ref<DiamondPosition[]>([])
const dialogVisible = ref(false)
const previewVisible = ref(false)
const dialogMode = ref<'create' | 'edit'>('create')
const currentItem = ref<DiamondPosition | null>(null)

// 选项数据
const iconTypeOptions = computed(() => ICON_TYPES)
const platformOptions = computed(() => PLATFORMS)

// 使用组合式函数
const {
  items,
  loading,
  total,
  queryParams,
  enabledItems,
  fetchItems,
  createItem,
  updateItem,
  deleteItem,
  batchDelete,
  batchUpdateStatus,
  updateSortOrder,
  search,
  resetSearch
} = useDiamondPosition()

// 页面方法
const handleSearch = () => {
  search()
}

const handleReset = () => {
  resetSearch()
}

const handleRefresh = () => {
  fetchItems()
}

const handleCreate = () => {
  currentItem.value = null
  dialogMode.value = 'create'
  dialogVisible.value = true
}

const handleEdit = (item: DiamondPosition) => {
  currentItem.value = { ...item }
  dialogMode.value = 'edit'
  dialogVisible.value = true
}

const handleDelete = async (item: DiamondPosition) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除金刚位"${item.name}"吗？`,
      '删除确认',
      {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }
    )
    
    await deleteItem(item.id!)
    await fetchItems()
  } catch (error) {
    // 用户取消删除
  }
}

const handleToggleStatus = async (item: DiamondPosition, status: 0 | 1) => {
  // 这个方法在组合式函数中已经实现
}

const handleBatchEnable = async () => {
  const ids = selectedItems.value.map(item => item.id!)
  await batchUpdateStatus(ids, 1)
  selectedItems.value = []
  await fetchItems()
}

const handleBatchDisable = async () => {
  const ids = selectedItems.value.map(item => item.id!)
  await batchUpdateStatus(ids, 0)
  selectedItems.value = []
  await fetchItems()
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的${selectedItems.value.length}个金刚位吗？`,
      '批量删除确认',
      {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }
    )
    
    const ids = selectedItems.value.map(item => item.id!)
    await batchDelete(ids)
    selectedItems.value = []
    await fetchItems()
  } catch (error) {
    // 用户取消删除
  }
}

const handleSortChange = async (newOrder: UpdateSortOrderRequest[]) => {
  await updateSortOrder(newOrder)
}

const handleViewModeChange = () => {
  selectedItems.value = []
}

const handlePageChange = (page: number) => {
  queryParams.pageNum = page
  fetchItems()
}

const handleSizeChange = (size: number) => {
  queryParams.pageSize = size
  queryParams.pageNum = 1
  fetchItems()
}

const handleDialogConfirm = async (formData: any) => {
  try {
    if (dialogMode.value === 'create') {
      await createItem(formData)
    } else {
      await updateItem(currentItem.value!.id!, formData)
    }
    
    dialogVisible.value = false
    await fetchItems()
  } catch (error) {
    // 错误已在组合式函数中处理
  }
}

const handleExport = (command: string) => {
  ElMessage.info(`导出${command.toUpperCase()}功能开发中...`)
}

// 生命周期
onMounted(() => {
  fetchItems()
})
</script>

<style scoped>
.diamond-position-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-section {
  margin-bottom: 20px;
}

.toolbar-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.content-section {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.ml-4 {
  margin-left: 16px;
}
</style>

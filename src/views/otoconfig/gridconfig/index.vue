<template>
  <div class="diamond-position-manage">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">金刚位配置管理</h1>
      <p class="page-description">管理应用首页金刚位的显示内容和排序</p>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索金刚位名称"
          prefix-icon="Search"
          clearable
          style="width: 300px"
          @input="handleSearch"
        />
        <el-select
          v-model="statusFilter"
          placeholder="状态筛选"
          style="width: 120px; margin-left: 12px"
          @change="handleStatusFilter"
        >
          <el-option label="全部" value="" />
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
        <el-select
          v-model="platformFilter"
          placeholder="平台筛选"
          style="width: 120px; margin-left: 12px"
          @change="handlePlatformFilter"
        >
          <el-option label="全部" value="" />
          <el-option label="全平台" value="ALL" />
          <el-option label="iOS" value="IOS" />
          <el-option label="Android" value="ANDROID" />
          <el-option label="H5" value="H5" />
        </el-select>
      </div>
      <div class="toolbar-right">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增金刚位
        </el-button>
        <el-button
          :disabled="selectedIds.length === 0"
          @click="handleBatchEdit"
        >
          批量编辑
        </el-button>
        <el-button @click="handlePreview">
          <el-icon><View /></el-icon>
          预览效果
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 金刚位网格配置区 -->
      <div class="grid-section">
        <div class="section-header">
          <h3>金刚位网格配置</h3>
          <el-button size="small" @click="handleSaveOrder">
            <el-icon><Check /></el-icon>
            保存排序
          </el-button>
        </div>
        <div class="diamond-grid" ref="gridContainer">
          <div
            v-for="item in sortedItems"
            :key="item.id"
            :data-id="item.id"
            class="diamond-item"
            :class="{ disabled: !item.isActive, dragging: draggedId === item.id }"
            draggable="true"
            @dragstart="handleDragStart"
            @dragover="handleDragOver"
            @dragenter="handleDragEnter"
            @dragleave="handleDragLeave"
            @drop="handleDrop"
            @dragend="handleDragEnd"
            @click="handleEdit(item)"
          >
            <div class="diamond-icon">
              <DiamondIcon
                :icon="item.icon || item.iconUrl"
                :icon-type="detectIconType(item.icon || item.iconUrl)"
                :size="48"
                :alt="item.title"
              />
            </div>
            <div class="diamond-name">{{ item.title }}</div>
            <div v-if="!item.isActive" class="disabled-mask">
              <span>已禁用</span>
            </div>
            <div class="item-actions">
              <el-button size="small" type="primary" @click.stop="handleEdit(item)">
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button
                size="small"
                :type="item.isActive === '1' ? 'warning' : 'success'"
                @click.stop="handleToggleStatus(item)"
              >
                <el-icon>
                  <component :is="item.isActive === '1' ? 'Hide' : 'View'" />
                </el-icon>
              </el-button>
              <el-button size="small" type="danger" @click.stop="handleDelete(item)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 多设备预览区 -->
      <div class="preview-section">
        <div class="section-header">
          <h3>多设备预览</h3>
          <div class="device-tabs">
            <el-radio-group v-model="previewDevice" @change="handleDeviceChange">
              <el-radio-button label="mobile">手机</el-radio-button>
              <el-radio-button label="tablet">平板</el-radio-button>
              <el-radio-button label="desktop">桌面</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="preview-container">
          <div :class="`preview-${previewDevice}`">
            <div class="preview-grid">
              <div
                v-for="item in enabledItems"
                :key="item.id"
                class="preview-item"
              >
                <div class="preview-icon">
                  <DiamondIcon
                    :icon="item.icon || item.iconUrl"
                    :icon-type="detectIconType(item.icon || item.iconUrl)"
                    :size="getPreviewIconSize(previewDevice)"
                    :alt="item.title"
                  />
                </div>
                <div class="preview-name">{{ item.title }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 金刚位列表 -->
    <div class="table-section">
      <div class="section-header">
        <h3>金刚位列表</h3>
        <div class="table-actions">
          <el-button
            size="small"
            :disabled="selectedIds.length === 0"
            @click="handleBatchDelete"
          >
            批量删除
          </el-button>
        </div>
      </div>
      <el-table
        :data="filteredItems"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="sortOrder" label="排序" width="80" sortable />
        <el-table-column label="图标" width="80">
          <template #default="{ row }">
            <DiamondIcon
              :icon="row.icon || row.iconUrl"
              :icon-type="detectIconType(row.icon || row.iconUrl)"
              :size="32"
              :alt="row.title"
            />
          </template>
        </el-table-column>
        <el-table-column prop="title" label="名称" min-width="120" />
        <el-table-column prop="route" label="跳转链接" min-width="200" show-overflow-tooltip />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.isActive === '1' ? 'success' : 'danger'">
              {{ row.isActive === '1' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="平台" width="100">
          <template #default="{ row }">
            <el-tag :type="getPlatformTagType(row.platform)" size="small">
              {{ getPlatformLabel(row.platform) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button
              size="small"
              :type="row.isActive === '1' ? 'warning' : 'success'"
              @click="handleToggleStatus(row)"
            >
              {{ row.isActive === '1' ? '禁用' : '启用' }}
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <DiamondPositionDialog
      v-model="dialogVisible"
      :form-data="currentItem"
      :is-edit="isEdit"
      @confirm="handleDialogConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  View,
  Check,
  Edit,
  Delete,
  Hide
} from '@element-plus/icons-vue'

// 组件导入
import DiamondIcon from '@/views/otoconfig/diamond-position/components/DiamondIcon.vue'
import DiamondPositionDialog from '@/views/otoconfig/diamond-position/components/DiamondPositionDialog.vue'

// API导入
import {
  getGridItemList,
  addGridItem,
  updateGridItem,
  delGridItem,
  updateGridItemStatus,
  batchSortGridItems
} from '@/api/otoconfig/gridconfig'
import type { GridItemVO, GridItemForm, GridItemQuery } from '@/api/otoconfig/gridconfig/types'

// 响应式数据
const searchKeyword = ref('')
const statusFilter = ref('')
const platformFilter = ref('')
const selectedIds = ref<number[]>([])
const dialogVisible = ref(false)
const isEdit = ref(false)
const currentItem = ref<GridItemForm | null>(null)
const previewDevice = ref('mobile')
const draggedId = ref<number | null>(null)
const gridContainer = ref<HTMLElement>()

// 数据列表
const items = ref<GridItemVO[]>([])
const loading = ref(false)

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 计算属性
const sortedItems = computed(() => {
  return [...items.value].sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
})

const enabledItems = computed(() => {
  return sortedItems.value.filter(item => item.isActive === '1')
})

const filteredItems = computed(() => {
  let result = [...items.value]

  if (searchKeyword.value) {
    result = result.filter(item =>
      item.title?.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  if (statusFilter.value) {
    result = result.filter(item => item.isActive === statusFilter.value)
  }

  if (platformFilter.value) {
    result = result.filter(item => item.platform === platformFilter.value)
  }

  return result
})

// 方法
const detectIconType = (icon: string): 'url' | 'svg' | 'base64' | 'emoji' => {
  if (!icon) return 'url'

  if (icon.startsWith('http') || icon.startsWith('/')) {
    return 'url'
  } else if (icon.startsWith('<svg') && icon.endsWith('</svg>')) {
    return 'svg'
  } else if (icon.startsWith('data:image/')) {
    return 'base64'
  } else if (icon.length <= 10) {
    return 'emoji'
  }

  return 'url'
}

const getPreviewIconSize = (device: string): number => {
  const sizeMap = {
    mobile: 40,
    tablet: 48,
    desktop: 56
  }
  return sizeMap[device] || 40
}

const getPlatformTagType = (platform: string): string => {
  const typeMap = {
    'ALL': 'primary',
    'IOS': 'success',
    'ANDROID': 'warning',
    'H5': 'info'
  }
  return typeMap[platform] || 'default'
}

const getPlatformLabel = (platform: string): string => {
  const labelMap = {
    'ALL': '全平台',
    'IOS': 'iOS',
    'ANDROID': 'Android',
    'H5': 'H5'
  }
  return labelMap[platform] || platform
}

const formatDate = (dateStr: string): string => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 数据加载
const loadData = async () => {
  loading.value = true
  try {
    const queryParams: GridItemQuery = {
      pageNum: pagination.currentPage,
      pageSize: pagination.pageSize,
      title: searchKeyword.value || undefined,
      isActive: statusFilter.value || undefined,
      platform: platformFilter.value || undefined
    }

    const response = await getGridItemList(queryParams)
    if (response.data) {
      items.value = response.data.rows || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleSearch = () => {
  pagination.currentPage = 1
  loadData()
}

const handleStatusFilter = () => {
  pagination.currentPage = 1
  loadData()
}

const handlePlatformFilter = () => {
  pagination.currentPage = 1
  loadData()
}

const handleAdd = () => {
  currentItem.value = null
  isEdit.value = false
  dialogVisible.value = true
}

const handleEdit = (item: GridItemVO) => {
  currentItem.value = { ...item }
  isEdit.value = true
  dialogVisible.value = true
}

const handleDelete = async (item: GridItemVO) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除金刚位"${item.title}"吗？`,
      '删除确认',
      {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }
    )

    await delGridItem(item.id)
    ElMessage.success('删除成功')
    await loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleToggleStatus = async (item: GridItemVO) => {
  try {
    const newStatus = item.isActive === '1' ? '0' : '1'
    await updateGridItemStatus(item.id, newStatus)
    item.isActive = newStatus
    ElMessage.success(`${newStatus === '1' ? '启用' : '禁用'}成功`)
  } catch (error) {
    console.error('状态切换失败:', error)
    ElMessage.error('状态切换失败')
  }
}

const handleBatchEdit = () => {
  ElMessage.info('批量编辑功能开发中...')
}

const handleBatchDelete = async () => {
  if (selectedIds.value.length === 0) {
    ElMessage.warning('请选择要删除的项目')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的${selectedIds.value.length}个金刚位吗？`,
      '批量删除确认',
      {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }
    )

    // 这里应该调用批量删除API
    for (const id of selectedIds.value) {
      await delGridItem(id)
    }

    ElMessage.success(`批量删除成功，共删除${selectedIds.value.length}项`)
    selectedIds.value = []
    await loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

const handlePreview = () => {
  ElMessage.info('预览功能开发中...')
}

const handleSaveOrder = async () => {
  try {
    const sortData = sortedItems.value.map((item, index) => ({
      id: item.id,
      sortOrder: index + 1
    }))

    await batchSortGridItems(sortData)
    ElMessage.success('排序保存成功')
    await loadData()
  } catch (error) {
    console.error('保存排序失败:', error)
    ElMessage.error('保存排序失败')
  }
}

const handleDeviceChange = () => {
  // 设备切换逻辑
}

const handleSelectionChange = (selection: GridItemVO[]) => {
  selectedIds.value = selection.map(item => item.id)
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const handleDialogConfirm = async (formData: GridItemForm) => {
  try {
    if (isEdit.value && currentItem.value) {
      await updateGridItem(formData)
      ElMessage.success('更新成功')
    } else {
      await addGridItem(formData)
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    await loadData()
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 拖拽相关方法
const handleDragStart = (event: DragEvent) => {
  const target = event.target as HTMLElement
  const itemElement = target.closest('.diamond-item') as HTMLElement
  if (itemElement) {
    draggedId.value = parseInt(itemElement.dataset.id || '0')
    event.dataTransfer?.setData('text/plain', '')
  }
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
}

const handleDragEnter = (event: DragEvent) => {
  event.preventDefault()
  const target = event.target as HTMLElement
  const itemElement = target.closest('.diamond-item') as HTMLElement
  if (itemElement) {
    itemElement.classList.add('drag-over')
  }
}

const handleDragLeave = (event: DragEvent) => {
  const target = event.target as HTMLElement
  const itemElement = target.closest('.diamond-item') as HTMLElement
  if (itemElement && !itemElement.contains(event.relatedTarget as Node)) {
    itemElement.classList.remove('drag-over')
  }
}

const handleDrop = async (event: DragEvent) => {
  event.preventDefault()

  const target = event.target as HTMLElement
  const itemElement = target.closest('.diamond-item') as HTMLElement

  if (itemElement && draggedId.value) {
    const targetId = parseInt(itemElement.dataset.id || '0')

    if (draggedId.value !== targetId) {
      // 重新排序逻辑
      const draggedIndex = items.value.findIndex(item => item.id === draggedId.value)
      const targetIndex = items.value.findIndex(item => item.id === targetId)

      if (draggedIndex !== -1 && targetIndex !== -1) {
        const [draggedItem] = items.value.splice(draggedIndex, 1)
        items.value.splice(targetIndex, 0, draggedItem)

        // 更新排序值
        items.value.forEach((item, index) => {
          item.sortOrder = index + 1
        })
      }
    }
  }

  // 清理拖拽状态
  document.querySelectorAll('.diamond-item').forEach(item => {
    item.classList.remove('drag-over')
  })
}

const handleDragEnd = () => {
  draggedId.value = null
  document.querySelectorAll('.diamond-item').forEach(item => {
    item.classList.remove('drag-over')
  })
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.diamond-position-manage {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 20px;
  margin-bottom: 20px;
}

.grid-section,
.preview-section {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.diamond-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
  min-height: 300px;
}

.diamond-item {
  position: relative;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 16px 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
}

.diamond-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.diamond-item.disabled {
  opacity: 0.6;
  background: #f5f7fa;
}

.diamond-item.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.diamond-item.drag-over {
  border-color: #67c23a;
  background: #f0f9ff;
}

.diamond-icon {
  margin-bottom: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
}

.diamond-name {
  font-size: 12px;
  color: #303133;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.disabled-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  font-size: 12px;
}

.item-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.diamond-item:hover .item-actions {
  opacity: 1;
}

.item-actions .el-button {
  padding: 4px;
  min-height: auto;
}

.preview-container {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 20px;
  background: #fafafa;
}

.preview-mobile {
  max-width: 375px;
  margin: 0 auto;
}

.preview-tablet {
  max-width: 768px;
  margin: 0 auto;
}

.preview-desktop {
  width: 100%;
}

.preview-grid {
  display: grid;
  gap: 12px;
}

.preview-mobile .preview-grid {
  grid-template-columns: repeat(4, 1fr);
}

.preview-tablet .preview-grid {
  grid-template-columns: repeat(6, 1fr);
}

.preview-desktop .preview-grid {
  grid-template-columns: repeat(8, 1fr);
}

.preview-item {
  text-align: center;
  padding: 8px;
}

.preview-icon {
  margin-bottom: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
}

.preview-name {
  font-size: 10px;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.table-section {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-actions {
  display: flex;
  gap: 8px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
  }

  .preview-section {
    order: -1;
  }
}

@media (max-width: 768px) {
  .diamond-position-manage {
    padding: 12px;
  }

  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }

  .diamond-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 12px;
  }

  .diamond-item {
    padding: 12px 8px;
  }

  .diamond-icon {
    height: 50px;
  }
}
</style>
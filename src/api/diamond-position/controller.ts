/**
 * 金刚位配置管理控制器
 * 支持多种图标类型和增强功能
 */

import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import type { 
  DiamondPosition, 
  DiamondPositionQuery, 
  CreateDiamondPositionRequest,
  UpdateDiamondPositionRequest,
  UpdateSortOrderRequest,
  BatchSortRequest,
  PaginatedResponse,
  ApiResponse,
  IconType,
  FileUploadParams,
  ImageUploadResponse,
  SvgUploadResponse,
  IconValidationResult
} from '@/types/diamond-position'

/**
 * 金刚位配置管理API控制器
 */
export class DiamondPositionController {
  private baseUrl = '/api/diamond-position'

  /**
   * 分页查询金刚位列表
   */
  async getList(query: DiamondPositionQuery): Promise<ApiResponse<PaginatedResponse<DiamondPosition>>> {
    try {
      // 模拟API调用 - 实际项目中应该调用真实的API
      const response = await this.mockApiCall<PaginatedResponse<DiamondPosition>>('GET', this.baseUrl, query)
      return response
    } catch (error) {
      console.error('获取金刚位列表失败:', error)
      throw error
    }
  }

  /**
   * 获取所有启用的金刚位
   */
  async getEnabledList(platform?: string): Promise<ApiResponse<DiamondPosition[]>> {
    try {
      const response = await this.mockApiCall<DiamondPosition[]>('GET', `${this.baseUrl}/enabled`, { platform })
      return response
    } catch (error) {
      console.error('获取启用金刚位列表失败:', error)
      throw error
    }
  }

  /**
   * 根据ID获取金刚位详情
   */
  async getById(id: number): Promise<ApiResponse<DiamondPosition>> {
    try {
      const response = await this.mockApiCall<DiamondPosition>('GET', `${this.baseUrl}/${id}`)
      return response
    } catch (error) {
      console.error('获取金刚位详情失败:', error)
      throw error
    }
  }

  /**
   * 创建金刚位
   */
  async create(data: CreateDiamondPositionRequest): Promise<ApiResponse<DiamondPosition>> {
    try {
      // 验证图标类型和内容
      const validationResult = await this.validateIcon(data.icon, data.iconType)
      if (!validationResult.data.valid) {
        throw new Error(validationResult.data.message)
      }

      const response = await this.mockApiCall<DiamondPosition>('POST', this.baseUrl, data)
      return response
    } catch (error) {
      console.error('创建金刚位失败:', error)
      throw error
    }
  }

  /**
   * 更新金刚位
   */
  async update(id: number, data: UpdateDiamondPositionRequest): Promise<ApiResponse<DiamondPosition>> {
    try {
      // 验证图标类型和内容
      if (data.icon && data.iconType) {
        const validationResult = await this.validateIcon(data.icon, data.iconType)
        if (!validationResult.data.valid) {
          throw new Error(validationResult.data.message)
        }
      }

      const response = await this.mockApiCall<DiamondPosition>('PUT', `${this.baseUrl}/${id}`, data)
      return response
    } catch (error) {
      console.error('更新金刚位失败:', error)
      throw error
    }
  }

  /**
   * 删除金刚位
   */
  async delete(id: number): Promise<ApiResponse<void>> {
    try {
      const response = await this.mockApiCall<void>('DELETE', `${this.baseUrl}/${id}`)
      return response
    } catch (error) {
      console.error('删除金刚位失败:', error)
      throw error
    }
  }

  /**
   * 批量删除金刚位
   */
  async batchDelete(ids: number[]): Promise<ApiResponse<void>> {
    try {
      const response = await this.mockApiCall<void>('DELETE', `${this.baseUrl}/batch`, { ids })
      return response
    } catch (error) {
      console.error('批量删除金刚位失败:', error)
      throw error
    }
  }

  /**
   * 切换金刚位状态
   */
  async toggleStatus(id: number, status: 0 | 1): Promise<ApiResponse<DiamondPosition>> {
    try {
      const response = await this.mockApiCall<DiamondPosition>('PATCH', `${this.baseUrl}/${id}/status`, { status })
      return response
    } catch (error) {
      console.error('切换金刚位状态失败:', error)
      throw error
    }
  }

  /**
   * 批量更新状态
   */
  async batchUpdateStatus(ids: number[], status: 0 | 1): Promise<ApiResponse<void>> {
    try {
      const response = await this.mockApiCall<void>('PATCH', `${this.baseUrl}/batch/status`, { ids, status })
      return response
    } catch (error) {
      console.error('批量更新状态失败:', error)
      throw error
    }
  }

  /**
   * 更新排序
   */
  async updateSortOrder(items: UpdateSortOrderRequest[]): Promise<ApiResponse<void>> {
    try {
      const response = await this.mockApiCall<void>('PUT', `${this.baseUrl}/sort-order`, { items })
      return response
    } catch (error) {
      console.error('更新排序失败:', error)
      throw error
    }
  }

  /**
   * 图片上传
   */
  async uploadImage(file: File, targetType: 'url' | 'base64'): Promise<ApiResponse<ImageUploadResponse>> {
    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('type', targetType)

      // 模拟文件上传
      const response = await this.mockFileUpload(formData, targetType)
      return response
    } catch (error) {
      console.error('图片上传失败:', error)
      throw error
    }
  }

  /**
   * SVG文件上传
   */
  async uploadSvg(file: File): Promise<ApiResponse<SvgUploadResponse>> {
    try {
      const formData = new FormData()
      formData.append('file', file)

      // 模拟SVG文件上传
      const response = await this.mockSvgUpload(formData)
      return response
    } catch (error) {
      console.error('SVG上传失败:', error)
      throw error
    }
  }

  /**
   * 验证图标
   */
  async validateIcon(icon: string, iconType: IconType): Promise<ApiResponse<IconValidationResult>> {
    try {
      const response = await this.mockIconValidation(icon, iconType)
      return response
    } catch (error) {
      console.error('图标验证失败:', error)
      throw error
    }
  }

  /**
   * 自动检测图标类型
   */
  async detectIconType(icon: string): Promise<ApiResponse<{ iconType: IconType }>> {
    try {
      const response = await this.mockIconTypeDetection(icon)
      return response
    } catch (error) {
      console.error('图标类型检测失败:', error)
      throw error
    }
  }

  /**
   * 模拟API调用
   */
  private async mockApiCall<T>(method: string, url: string, data?: any): Promise<ApiResponse<T>> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300))

    // 模拟响应数据
    return {
      success: true,
      data: data as T,
      message: '操作成功',
      timestamp: new Date().toISOString()
    }
  }

  /**
   * 模拟文件上传
   */
  private async mockFileUpload(formData: FormData, targetType: 'url' | 'base64'): Promise<ApiResponse<ImageUploadResponse>> {
    await new Promise(resolve => setTimeout(resolve, 1000))

    const file = formData.get('file') as File
    
    return {
      success: true,
      data: {
        url: targetType === 'url' ? `https://example.com/uploads/${file.name}` : undefined,
        base64: targetType === 'base64' ? 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==' : undefined,
        originalName: file.name,
        size: file.size,
        mimeType: file.type
      },
      message: '上传成功'
    }
  }

  /**
   * 模拟SVG上传
   */
  private async mockSvgUpload(formData: FormData): Promise<ApiResponse<SvgUploadResponse>> {
    await new Promise(resolve => setTimeout(resolve, 800))

    const file = formData.get('file') as File
    
    return {
      success: true,
      data: {
        svgContent: '<svg viewBox="0 0 24 24"><path d="M12 2L2 7v10c0 5.55 3.84 10 9 10s9-4.45 9-10V7l-10-5z"/></svg>',
        originalName: file.name,
        size: file.size
      },
      message: 'SVG文件读取成功'
    }
  }

  /**
   * 模拟图标验证
   */
  private async mockIconValidation(icon: string, iconType: IconType): Promise<ApiResponse<IconValidationResult>> {
    await new Promise(resolve => setTimeout(resolve, 200))

    const validationRules = {
      url: /^(https?:\/\/|\/)/,
      svg: /^<svg[\s\S]*<\/svg>$/,
      base64: /^data:image\//,
      emoji: /^.{1,10}$/
    }

    const isValid = validationRules[iconType].test(icon)

    return {
      success: true,
      data: {
        valid: isValid,
        message: isValid ? '验证通过' : `${iconType}类型图标格式不正确`
      },
      message: '验证完成'
    }
  }

  /**
   * 模拟图标类型检测
   */
  private async mockIconTypeDetection(icon: string): Promise<ApiResponse<{ iconType: IconType }>> {
    await new Promise(resolve => setTimeout(resolve, 100))

    let detectedType: IconType = 'url'

    if (icon.startsWith('http') || icon.startsWith('/')) {
      detectedType = 'url'
    } else if (icon.startsWith('<svg') && icon.endsWith('</svg>')) {
      detectedType = 'svg'
    } else if (icon.startsWith('data:image/')) {
      detectedType = 'base64'
    } else if (icon.length <= 10) {
      detectedType = 'emoji'
    }

    return {
      success: true,
      data: { iconType: detectedType },
      message: '检测完成'
    }
  }
}

// 导出单例实例
export const diamondPositionController = new DiamondPositionController()
